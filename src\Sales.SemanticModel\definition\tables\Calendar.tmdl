table Calendar
	dataCategory: Time

	column Day
		dataType: int64
		formatString: 0
		summarizeBy: none
		sourceColumn: Day

	column Month
		dataType: int64
		formatString: 0
		summarizeBy: none
		sourceColumn: Month

	column Quarter
		dataType: string
		summarizeBy: none
		sourceColumn: Quarter

	column Year
		dataType: int64
		formatString: 0
		summarizeBy: none
		sourceColumn: Year

	column Date
		dataType: dateTime
		isKey
		formatString: Long Date
		summarizeBy: none
		sourceColumn: Date

	column FullDateAlternateKey
		dataType: dateTime
		formatString: Long Date
		summarizeBy: none
		sourceColumn: FullDateAlternateKey
		isHidden
		isAvailableInMdx: false

	column 'Month Name'
		dataType: string
		summarizeBy: none
		sourceColumn: Month Name
		sortByColumn: Month

	column Year-Month
		dataType: dateTime
		formatString: mmm yyyy
		summarizeBy: none
		sourceColumn: Year-Month

	column 'Week Number'
		dataType: int64
		formatString: 0
		summarizeBy: none
		sourceColumn: Week Number

	column 'Day of Week'
		dataType: int64
		formatString: 0
		summarizeBy: none
		sourceColumn: Day of Week

	column 'Day Name'
		dataType: string
		summarizeBy: none
		sourceColumn: Day Name
		sortByColumn: 'Day of Week'

	column 'Fiscal Quarter'
		dataType: string
		summarizeBy: none
		sourceColumn: Fiscal Quarter

	hierarchy Year-Month-Day

		level Year
			column: Year

		level Month
			column: Month

		level Day
			column: Day

	partition Calendar = m
		mode: import
		source = ```
			let
			    Source = Sql.Database(#"Parameter_Server", #"Parameter_Database"),
			    DimDate = Source{[Schema="dbo",Item="DimDate"]}[Data],
			    SelectColumns = Table.SelectColumns(DimDate,{"DateKey", "FullDateAlternateKey", "DayNumberOfWeek", "EnglishDayNameOfWeek", "DayNumberOfMonth", "DayNumberOfYear", "WeekNumberOfYear", "EnglishMonthName", "MonthNumberOfYear", "CalendarQuarter", "CalendarYear", "CalendarSemester", "FiscalQuarter", "FiscalYear", "FiscalSemester"})
			in
			    SelectColumns
			```