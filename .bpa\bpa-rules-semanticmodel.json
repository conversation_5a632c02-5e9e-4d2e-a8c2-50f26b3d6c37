[{"ID": "AVOID_FLOATING_POINT_DATA_TYPES", "Name": "[Performance] Do not use floating point data types", "Category": "Performance", "Description": "The \"Double\" floating point data type should be avoided, as it can result in unpredictable roundoff errors and decreased performance in certain scenarios. Use \"Int64\" or \"Decimal\" where appropriate (but note that \"Decimal\" is limited to 4 digits after the decimal sign).", "Severity": 2, "Scope": "DataColumn, CalculatedColumn, CalculatedTableColumn", "Expression": "DataType = \"Double\"", "FixExpression": "DataType = DataType.Decimal", "CompatibilityLevel": 1200}, {"ID": "ISAVAILABLEINMDX_FALSE_NONATTRIBUTE_COLUMNS", "Name": "[Performance] Set IsAvailableInMdx to false on non-attribute columns. Solution: Set TMDL property 'isAvailableInMdx: false", "Category": "Performance", "Description": "To speed up processing time and conserve memory after processing, attribute hierarchies should not be built for columns that are never used for slicing by MDX clients. In other words, all hidden columns that are not used as a Sort By Column or referenced in user hierarchies should have their IsAvailableInMdx property set to false.\r\nReference: https://blog.crossjoin.co.uk/2018/07/02/isavailableinmdx-ssas-tabular/", "Severity": 2, "Scope": "DataColumn, CalculatedColumn, CalculatedTableColumn", "Expression": "IsAvailableInMDX\r\nand\r\n\n(IsHidden or Table.IsHidden)\r\nand\r\n\nnot UsedInSortBy.Any() \r\nand\r\n\nnot UsedInHierarchies.Any()\r\nand\r\nnot UsedInVariations.Any()\r\nand\r\nSortByColumn = null", "FixExpression": "IsAvailableInMDX = false", "CompatibilityLevel": 1200}, {"ID": "AVOID_BI-DIRECTIONAL_RELATIONSHIPS_AGAINST_HIGH-CARDINALITY_COLUMNS", "Name": "[Performance] Avoid bi-directional relationships against high-cardinality columns", "Category": "Performance", "Description": "For best performance, it is recommended to avoid using bi-directional relationships against high-cardinality columns. In order to run this rule, you must first run the script shown here: https://www.elegantbi.com/post/vertipaqintabulareditor", "Severity": 2, "Scope": "DataColumn, CalculatedColumn, CalculatedTableColumn", "Expression": "UsedInRelationships.Any(CrossFilteringBehavior == CrossFilteringBehavior.BothDirections)\n\nand\n\nConvert.ToInt64(GetAnnotation(\"Vertipaq_Cardinality\")) > 100000", "CompatibilityLevel": 1200}, {"ID": "REDUCE_USAGE_OF_LONG-LENGTH_COLUMNS_WITH_HIGH_CARDINALITY", "Name": "[Performance] Reduce usage of long-length columns with high cardinality", "Category": "Performance", "Description": "It is best to avoid lengthy text columns. This is especially true if the column has many unique values. These types of columns can cause longer processing times, bloated model sizes, as well as slower user queries. Long length is defined as more than 100 characters.", "Severity": 2, "Scope": "DataColumn, CalculatedColumn, CalculatedTableColumn", "Expression": "Convert.ToInt64(GetAnnotation(\"LongLengthRowCount\")) > 500000", "CompatibilityLevel": 1200}, {"ID": "SPLIT_DATE_AND_TIME", "Name": "[Performance] Split date and time", "Category": "Performance", "Description": "This rule finds datetime columns that have values not at midnight. To maximize performance, the time element should be split from date element (or the time component should be rounded to midnight as this will reduce column cardinality).\r\nReference: https://www.sqlbi.com/articles/separate-date-and-time-in-powerpivot-and-bism-tabular/", "Severity": 2, "Scope": "DataColumn, CalculatedColumn, CalculatedTableColumn", "Expression": "Convert.ToInt32(GetAnnotation(\"DateTimeWithHourMinSec\")) > 0", "CompatibilityLevel": 1200}, {"ID": "LARGE_TABLES_SHOULD_BE_PARTITIONED", "Name": "[Performance] Large tables should be partitioned", "Category": "Performance", "Description": "Large tables should be partitioned in order to optimize processing. In order for this rule to run properly, you must run the script shown here: https://www.elegantbi.com/post/vertipaqintabulareditor", "Severity": 2, "Scope": "Table", "Expression": "Convert.ToInt64(GetAnnotation(\"Vertipaq_RowCount\")) > 25000000\r\nand\r\nPartitions.Count = 1", "CompatibilityLevel": 1200}, {"ID": "REDUCE_USAGE_OF_CALCULATED_COLUMNS_THAT_USE_THE_RELATED_FUNCTION", "Name": "[Performance] Reduce usage of calculated columns that use the RELATED function", "Category": "Performance", "Description": "Calculated columns do not compress as well as data columns and may cause longer processing times. As such, calculated columns should be avoided if possible. One scenario where they may be easier to avoid is if they use the RELATED function.\r\nReference: https://www.sqlbi.com/articles/storage-differences-between-calculated-columns-and-calculated-tables/", "Severity": 2, "Scope": "CalculatedColumn", "Expression": "RegEx.IsMatch(Expression,\"(?i)RELATED\\s*\\(\")", "CompatibilityLevel": 1200}, {"ID": "SNOWFLAKE_SCHEMA_ARCHITECTURE", "Name": "[Performance] Consider a star-schema instead of a snowflake architecture", "Category": "Performance", "Description": "Generally speaking, a star-schema is the optimal architecture for tabular models. That being the case, there are valid cases to use a snowflake approach. Please check your model and consider moving to a star-schema architecture.\r\nReference: https://docs.microsoft.com/power-bi/guidance/star-schema", "Severity": 2, "Scope": "Table, CalculatedTable", "Expression": "UsedInRelationships.Any(current.Name == FromTable.Name)\r\nand\r\nUsedInRelationships.Any(current.Name == ToTable.Name)", "CompatibilityLevel": 1200}, {"ID": "MODEL_SHOULD_HAVE_A_DATE_TABLE", "Name": "[Performance] Model should have a date table", "Category": "Performance", "Description": "Generally speaking, models should generally have a date table. Models that do not have a date table generally are not taking advantage of features such as time intelligence or may not have a properly structured architecture.", "Severity": 2, "Scope": "Model", "Expression": "Tables.Any(DataCategory == \"Time\" && Columns.Any(IsKey == true && DataType == \"DateTime\")) == false", "CompatibilityLevel": 1200}, {"ID": "DATE/CALENDAR_TABLES_SHOULD_BE_MARKED_AS_A_DATE_TABLE", "Name": "[Performance] Date/calendar tables should be marked as a date table", "Category": "Performance", "Description": "This rule looks for tables that contain the words 'date' or 'calendar' as they should likely be marked as a date table.\r\nReference: https://docs.microsoft.com/power-bi/transform-model/desktop-date-tables", "Severity": 2, "Scope": "Table, CalculatedTable", "Expression": "(Name.ToUpper().Contains(\"DATE\") or Name.ToUpper().Contains(\"CALENDAR\"))\n\nand\n\n(\nDataCategory <> \"Time\"\n\nor\n\nColumns.Any(IsKey == true && DataType == \"DateTime\") == false\n)", "CompatibilityLevel": 1200}, {"ID": "REMOVE_AUTO-DATE_TABLE", "Name": "[Performance] Remove auto-date table", "Category": "Performance", "Description": "Avoid using auto-date tables. Make sure to turn off auto-date table in the settings in Power BI Desktop. This will save memory resources. \r\nReference: https://www.youtube.com/watch?v=xu3uDEHtCrg", "Severity": 2, "Scope": "Table, CalculatedTable", "Expression": "ObjectTypeName == \"Calculated Table\"\n\r\nand\r\n\n(\nName.StartsWith(\"DateTableTemplate_\") \n\nor \n\nName.StartsWith(\"LocalDateTable_\")\n)", "CompatibilityLevel": 1200}, {"ID": "AVOID_EXCESSIVE_BI-DIRECTIONAL_OR_MANY-TO-MANY_RELATIONSHIPS", "Name": "[Performance] Avoid excessive bi-directional or many-to-many relationships", "Category": "Performance", "Description": "Limit use of b-di and many-to-many relationships. This rule flags the model if more than 30% of relationships are bi-di or many-to-many.\r\nReference: https://www.sqlbi.com/articles/bidirectional-relationships-and-ambiguity-in-dax/", "Severity": 2, "Scope": "Model", "Expression": "(\r\n\nRelationships.Where(CrossFilteringBehavior == CrossFilteringBehavior.BothDirections).Count()\r\n\n+\r\n\nRelationships.Where(FromCardinality.ToString() == \"Many\" && ToCardinality.ToString() == \"Many\").Count()\r\n\n)\r\n\n\n/\r\n\n\nMath.Max(Convert.ToDecimal(Relationships.Count)\n\n,1)> 0.3", "CompatibilityLevel": 1200}, {"ID": "LIMIT_ROW_LEVEL_SECURITY_(RLS)_LOGIC", "Name": "[Performance] Limit row level security (RLS) logic", "Category": "Performance", "Description": "Try to simplify the DAX used for row level security. Usage of the functions within this rule can likely be offloaded to the upstream systems (data warehouse).", "Severity": 2, "Scope": "Table, CalculatedTable", "Expression": "RowLevelSecurity.Any(RegEx.IsMatch(it.Replace(\" \",\"\"),\"(?i)RIGHT\\s*\\(\"))\r\nor\r\nRowLevelSecurity.Any(RegEx.IsMatch(it.Replace(\" \",\"\"),\"(?i)LEFT\\s*\\(\"))\r\nor\r\nRowLevelSecurity.Any(RegEx.IsMatch(it.Replace(\" \",\"\"),\"(?i)UPPER\\s*\\(\"))\r\nor\r\nRowLevelSecurity.Any(RegEx.IsMatch(it.Replace(\" \",\"\"),\"(?i)LOWER\\s*\\(\"))\r\nor\r\nRowLevelSecurity.Any(RegEx.IsMatch(it.Replace(\" \",\"\"),\"(?i)FIND\\s*\\(\"))\r\n", "CompatibilityLevel": 1200}, {"ID": "MODEL_USING_DIRECT_QUERY_AND_NO_AGGREGATIONS", "Name": "[Performance] Consider using aggregations if using Direct Query in Power BI", "Category": "Performance", "Description": "If using Direct Query in Power BI Premium, you may want to consider using aggregations in order to boost performance.\r\nReference: https://docs.microsoft.com/power-bi/transform-model/desktop-aggregations", "Severity": 1, "Scope": "Model", "Expression": "Tables.Any(ObjectTypeName == \"Table (DirectQuery)\")\r\nand\r\n\n\nAllColumns.Any(AlternateOf != null) == false\r\nand \r\nDefaultPowerBIDataSourceVersion.ToString() == \"PowerBI_V3\"", "CompatibilityLevel": 1200}, {"ID": "AVOID_USING_MANY-TO-MANY_RELATIONSHIPS_ON_TABLES_USED_FOR_DYNAMIC_ROW_LEVEL_SECURITY", "Name": "[Performance] Avoid using many-to-many relationships on tables used for dynamic row level security", "Category": "Performance", "Description": "Using many-to-many relationships on tables which use dynamic row level security can cause serious query performance degradation. This pattern's performance problems compound when snowflaking multiple many-to-many relationships against a table which contains row level security. Instead, use one of the patterns shown in the article below where a single dimension table relates many-to-one to a security table.\r\n\r\nReference: https://www.elegantbi.com/post/dynamicrlspatterns", "Severity": 3, "Scope": "Table", "Expression": "UsedInRelationships.Any(FromCardinality == \"Many\" and ToCardinality== \"Many\")\r\nand\r\nRowLevelSecurity.Any(it.Length > 0)", "CompatibilityLevel": 1200}, {"ID": "UNPIVOT_PIVOTED_(MONTH)_DATA", "Name": "[Performance] Unpivot pivoted (month) data", "Category": "Performance", "Description": "Avoid using pivoted data in your tables. This rule checks specifically for pivoted data by month.\r\nReference: https://www.elegantbi.com/post/top10bestpractices", "Severity": 2, "Scope": "Table, CalculatedTable", "Expression": "Columns.Any(Name.ToUpper().Contains(\"JAN\") && (DataType == DataType.Int64 || DataType == DataType.Decimal || DataType == DataType.Double))\nand\nColumns.Any(Name.ToUpper().Contains(\"FEB\") && (DataType == DataType.Int64 || DataType == DataType.Decimal || DataType == DataType.Double))\nand\nColumns.Any(Name.ToUpper().Contains(\"MAR\") && (DataType == DataType.Int64 || DataType == DataType.Decimal || DataType == DataType.Double))\nand\nColumns.Any(Name.ToUpper().Contains(\"APR\") && (DataType == DataType.Int64 || DataType == DataType.Decimal || DataType == DataType.Double))\nand\nColumns.Any(Name.ToUpper().Contains(\"MAY\") && (DataType == DataType.Int64 || DataType == DataType.Decimal || DataType == DataType.Double))\nand\nColumns.Any(Name.ToUpper().Contains(\"JUN\") && (DataType == DataType.Int64 || DataType == DataType.Decimal || DataType == DataType.Double))", "CompatibilityLevel": 1200}, {"ID": "MANY-TO-MANY_RELATIONSHIPS_SHOULD_BE_SINGLE-DIRECTION", "Name": "[Performance] Many-to-many relationships should be single-direction", "Category": "Performance", "Severity": 2, "Scope": "Relationship", "Expression": "FromCardinality == \"Many\"\n\r\nand\r\n\nToCardinality == \"Many\"\r\n\nand\r\n\nCrossFilteringBehavior == \"BothDirections\"", "CompatibilityLevel": 1200}, {"ID": "REMOVE_REDUNDANT_COLUMNS_IN_RELATED_TABLES", "Name": "[Performance] Remove redundant columns in related tables", "Category": "Performance", "Description": "Removing unnecessary columns reduces model size and speeds up data loading.", "Severity": 2, "Scope": "DataColumn, CalculatedColumn, CalculatedTableColumn", "Expression": "UsedInRelationships.Any() == false \r\nand\r\nModel.AllColumns.Any(Name == current.Name and Table.Name != current.Table.Name and Table.UsedInRelationships.Any(FromTable.Name == current.Table.Name))", "CompatibilityLevel": 1200}, {"ID": "MEASURES_USING_TIME_INTELLIGENCE_AND_MODEL_IS_USING_DIRECT_QUERY", "Name": "[Performance] Measures using time intelligence and model is using Direct Query", "Category": "Performance", "Description": "At present, time intelligence functions are known to not perform as well when using Direct Query. If you are having performance issues, you may want to try alternative solutions such as adding columns in the fact table that show previous year or previous month data.", "Severity": 2, "Scope": "Measure, CalculationItem", "Expression": "Model.Tables.Any(ObjectTypeName == \"Table (DirectQuery)\")\r\nand\r\n(\r\nRegEx.IsMatch(Expression,\"CLOSINGBALANCEMONTH\\s*\\(\")\r\nor\r\nRegEx.IsMatch(Expression,\"CLOSINGBALANCEQUARTER\\s*\\(\")\r\nor\r\nRegEx.IsMatch(Expression,\"CLOSINGBALANCEYEAR\\s*\\(\")\r\nor\r\nRegEx.IsMatch(Expression,\"DATEADD\\s*\\(\")\r\nor\r\nRegEx.IsMatch(Expression,\"DATESBETWEEN\\s*\\(\")\r\nor\r\nRegEx.IsMatch(Expression,\"DATESINPERIOD\\s*\\(\")\r\nor\r\nRegEx.IsMatch(Expression,\"DATESMTD\\s*\\(\")\r\nor\r\nRegEx.IsMatch(Expression,\"DATESQTD\\s*\\(\")\r\nor\r\nRegEx.IsMatch(Expression,\"DATESYTD\\s*\\(\")\r\nor\r\nRegEx.IsMatch(Expression,\"ENDOFMONTH\\s*\\(\")\r\nor\r\nRegEx.IsMatch(Expression,\"ENDOFQUARTER\\s*\\(\")\r\nor\r\nRegEx.IsMatch(Expression,\"ENDOFYEAR\\s*\\(\")\r\nor\r\nRegEx.IsMatch(Expression,\"FIRSTDATE\\s*\\(\")\r\nor\r\nRegEx.IsMatch(Expression,\"FIRSTNONBLANK\\s*\\(\")\r\nor\r\nRegEx.IsMatch(Expression,\"FIRSTNONBLANKVALUE\\s*\\(\")\r\nor\r\nRegEx.IsMatch(Expression,\"LASTDATE\\s*\\(\")\r\nor\r\nRegEx.IsMatch(Expression,\"LASTNONBLANK\\s*\\(\")\r\nor\r\nRegEx.IsMatch(Expression,\"LASTNONBLANKVALUE\\s*\\(\")\r\nor\r\nRegEx.IsMatch(Expression,\"NEXTDAY\\s*\\(\")\r\nor\r\nRegEx.IsMatch(Expression,\"NEXTMONTH\\s*\\(\")\r\nor\r\nRegEx.IsMatch(Expression,\"NEXTQUARTER\\s*\\(\")\r\nor\r\nRegEx.IsMatch(Expression,\"NEXTYEAR\\s*\\(\")\r\nor\r\nRegEx.IsMatch(Expression,\"OPENINGBALANCEMONTH\\s*\\(\")\r\nor\r\nRegEx.IsMatch(Expression,\"OPENINGBALANCEQUARTER\\s*\\(\")\r\nor\r\nRegEx.IsMatch(Expression,\"OPENINGBALANCEYEAR\\s*\\(\")\r\nor\r\nRegEx.IsMatch(Expression,\"PARALLELPERIOD\\s*\\(\")\r\nor\r\nRegEx.IsMatch(Expression,\"PREVIOUSDAY\\s*\\(\")\r\nor\r\nRegEx.IsMatch(Expression,\"PREVIOUSMONTH\\s*\\(\")\r\nor\r\nRegEx.IsMatch(Expression,\"PREVIOUSQUARTER\\s*\\(\")\r\nor\r\nRegEx.IsMatch(Expression,\"PREVIOUSYEAR\\s*\\(\")\r\nor\r\nRegEx.IsMatch(Expression,\"SAMEPERIODLASTYEAR\\s*\\(\")\r\nor\r\nRegEx.IsMatch(Expression,\"STARTOFMONTH\\s*\\(\")\r\nor\r\nRegEx.IsMatch(Expression,\"STARTOFQUARTER\\s*\\(\")\r\nor\r\nRegEx.IsMatch(Expression,\"STARTOFYEAR\\s*\\(\")\r\nor\r\nRegEx.IsMatch(Expression,\"TOTALMTD\\s*\\(\")\r\nor\r\nRegEx.IsMatch(Expression,\"TOTALQTD\\s*\\(\")\r\nor\r\nRegEx.IsMatch(Expression,\"TOTALYTD\\s*\\(\")\r\n)", "CompatibilityLevel": 1200}, {"ID": "REDUCE_NUMBER_OF_CALCULATED_COLUMNS", "Name": "[Performance] Reduce number of calculated columns", "Category": "Performance", "Description": "Calculated columns do not compress as well as data columns so they take up more memory. They also slow down processing times for both the table as well as process recalc. Offload calculated column logic to your data warehouse and turn these calculated columns into data columns.\r\nReference: https://www.elegantbi.com/post/top10bestpractices", "Severity": 2, "Scope": "Model", "Expression": "AllColumns.Where(Type.ToString() == \"Calculated\").Count() > 5", "CompatibilityLevel": 1200}, {"ID": "CHECK_IF_BI-DIRECTIONAL_AND_MANY-TO-MANY_RELATIONSHIPS_ARE_VALID", "Name": "[Performance] Check if bi-directional and many-to-many relationships are valid", "Category": "Performance", "Description": "Bi-directional and many-to-many relationships may cause performance degradation or even have unintended consequences. Make sure to check these specific relationships to ensure they are working as designed and are actually necessary.\r\nReference: https://www.sqlbi.com/articles/bidirectional-relationships-and-ambiguity-in-dax/", "Severity": 1, "Scope": "Relationship", "Expression": "FromCardinality.ToString() = \"Many\" and ToCardinality.ToString() = \"Many\"\r\nor\r\nCrossFilteringBehavior == CrossFilteringBehavior.BothDirections", "CompatibilityLevel": 1200}, {"ID": "CHECK_IF_DYNAMIC_ROW_LEVEL_SECURITY_(RLS)_IS_NECESSARY", "Name": "[Performance] Check if dynamic row level security (RLS) is necessary", "Category": "Performance", "Description": "Usage of dynamic row level security (RLS) can add memory and performance overhead. Please research the pros/cons of using it.\r\nReference: https://docs.microsoft.com/power-bi/admin/service-admin-rls", "Severity": 1, "Scope": "TablePermission", "Expression": "RegEx.IsMatch(Expression,\"(?i)USERNAME\\(\")\r\nor\r\nRegEx.IsMatch(Expression,\"(?i)USERPRINCIPALNAME\\(\")", "CompatibilityLevel": 1200}, {"ID": "DAX_COLUMNS_FULLY_QUALIFIED", "Name": "[DAX Expressions] Column references should be fully qualified", "Category": "DAX Expressions", "Description": "Using fully qualified column references makes it easier to distinguish between column and measure references, and also helps avoid certain errors. When referencing a column in DAX, first specify the table name, then specify the column name in square brackets.\r\nReference: https://www.elegantbi.com/post/top10bestpractices", "Severity": 3, "Scope": "Measure, KPI, TablePermission, CalculationItem", "Expression": "DependsOn.Any(Key.ObjectType = \"Column\" and Value.Any(not FullyQualified))", "CompatibilityLevel": 1200}, {"ID": "DAX_MEASURES_UNQUALIFIED", "Name": "[DAX Expressions] Measure references should be unqualified", "Category": "DAX Expressions", "Description": "Using unqualified measure references makes it easier to distinguish between column and measure references, and also helps avoid certain errors. When referencing a measure using DAX, do not specify the table name. Use only the measure name in square brackets.\r\nReference: https://www.elegantbi.com/post/top10bestpractices", "Severity": 3, "Scope": "Measure, CalculatedColumn, CalculatedTable, KPI, CalculationItem", "Expression": "DependsOn.Any(Key.ObjectType = \"Measure\" and Value.Any(FullyQualified))", "CompatibilityLevel": 1200}, {"ID": "AVOID_DUPLICATE_MEASURES", "Name": "[DAX Expressions] No two measures should have the same definition", "Category": "DAX Expressions", "Description": "Two measures with different names and defined by the same DAX expression should be avoided to reduce redundancy.", "Severity": 2, "Scope": "Measure", "Expression": "Model.AllMeasures.Any(Expression.Replace(\" \",\"\").Replace(\"\\n\",\"\").Replace(\"\\r\",\"\").Replace(\"\\t\",\"\") = outerIt.Expression.Replace(\" \",\"\").Replace(\"\\n\",\"\").Replace(\"\\r\",\"\").Replace(\"\\t\",\"\") and it <> outerIt)", "CompatibilityLevel": 1200}, {"ID": "USE_THE_TREATAS_FUNCTION_INSTEAD_OF_INTERSECT", "Name": "[DAX Expressions] Use the TREATAS function instead of INTERSECT for virtual relationships", "Category": "DAX Expressions", "Description": "The TREATAS function is more efficient and provides better performance than the INTERSECT function when used in virutal relationships.\r\nReference:  https://www.sqlbi.com/articles/propagate-filters-using-treatas-in-dax/", "Severity": 2, "Scope": "Measure, CalculationItem", "Expression": "RegEx.IsMatch(Expression,\"(?i)INTERSECT\\s*\\(\")", "CompatibilityLevel": 1400}, {"ID": "USE_THE_DIVIDE_FUNCTION_FOR_DIVISION", "Name": "[DAX Expressions] Use the DIVIDE function for division", "Category": "DAX Expressions", "Description": "Use the DIVIDE  function instead of using \"/\". The DIVIDE function resolves divide-by-zero cases. As such, it is recommended to use to avoid errors.\r\n\r\nReference: https://docs.microsoft.com/power-bi/guidance/dax-divide-function-operator", "Severity": 2, "Scope": "Measure, CalculatedColumn, CalculationItem", "Expression": "RegEx.IsMatch(Expression,\"\\]\\s*\\/(?!\\/)(?!\\*)\")\r\nor\r\nRegEx.IsMatch(Expression,\"\\)\\s*\\/(?!\\/)(?!\\*)\")", "CompatibilityLevel": 1200}, {"ID": "AVOID_USING_THE_IFERROR_FUNCTION", "Name": "[DAX Expressions] Avoid using the IFERROR function", "Category": "DAX Expressions", "Description": "Avoid using the IFERROR function as it may cause performance degradation. If you are concerned about a divide-by-zero error, use the DIVIDE function as it naturally resolves such errors as blank (or you can customize what should be shown in case of such an error).\r\nReference: https://www.elegantbi.com/post/top10bestpractices", "Severity": 2, "Scope": "Measure, CalculatedColumn", "Expression": "RegEx.IsMatch(Expression,\"(?i)IFERROR\\s*\\(\")", "CompatibilityLevel": 1200}, {"ID": "MEASURES_SHOULD_NOT_BE_DIRECT_REFERENCES_OF_OTHER_MEASURES", "Name": "[DAX Expressions] Measures should not be direct references of other measures", "Category": "DAX Expressions", "Description": "This rule identifies measures which are simply a reference to another measure. As an example, consider a model with two measures: [MeasureA] and [MeasureB]. This rule would be triggered for MeasureB if MeasureB's DAX was MeasureB:=[MeasureA]. Such duplicative measures should be removed.", "Severity": 2, "Scope": "Measure", "Expression": "Model.AllMeasures.Any(DaxObjectName == current.Expression)", "CompatibilityLevel": 1200}, {"ID": "FILTER_COLUMN_VALUES", "Name": "[DAX Expressions] Filter column values with proper syntax", "Category": "DAX Expressions", "Description": "Instead of using this pattern FILTER('Table','Table'[Column]=\"Value\") for the filter parameters of a CALCULATE or CALCULATETABLE function, use one of the options below. As far as whether to use the KEEPFILTERS function, see the second reference link below.\r\n\r\nOption 1: KEEPFILTERS('Table'[Column]=\"Value\")\r\nOption 2: 'Table'[Column]=\"Value\"\r\n\r\nReference: https://docs.microsoft.com/power-bi/guidance/dax-avoid-avoid-filter-as-filter-argument\r\nReference: https://www.sqlbi.com/articles/using-keepfilters-in-dax/", "Severity": 2, "Scope": "Measure, CalculatedColumn, CalculationItem", "Expression": "RegEx.IsMatch(Expression,\"(?i)CALCULATE\\s*\\(\\s*[^,]+,\\s*(?i)FILTER\\s*\\(\\s*\\'*[A-Za-z0-9 _]+'*\\s*,\\s*\\'*[A-Za-z0-9 _]+\\'*\\[[A-Za-z0-9 _]+\\]\")\r\nor\r\nRegEx.IsMatch(Expression,\"(?i)CALCULATETABLE\\s*\\([^,]*,\\s*(?i)FILTER\\s*\\(\\s*\\'*[A-Za-z0-9 _]+\\'*,\\s*\\'*[A-Za-z0-9 _]+\\'*\\[[A-Za-z0-9 _]+\\]\")", "CompatibilityLevel": 1200}, {"ID": "FILTER_MEASURE_VALUES_BY_COLUMNS", "Name": "[DAX Expressions] Filter measure values by columns, not tables", "Category": "DAX Expressions", "Description": "Instead of using this pattern FILTER('Table',[Measure]>Value) for the filter parameters of a CALCULATE or CALCULATETABLE function, use one of the options below (if possible). Filtering on a specific column will produce a smaller table for the engine to process, thereby enabling faster performance. Using the VALUES function or the ALL function depends on the desired measure result.\r\n\r\nOption 1: FILTER(VALUES('Table'[Column]),[Measure] > Value)\r\nOption 2: FILTER(ALL('Table'[Column]),[Measure] > Value)\r\n\r\nReference: https://docs.microsoft.com/power-bi/guidance/dax-avoid-avoid-filter-as-filter-argument", "Severity": 2, "Scope": "Measure, CalculatedColumn, CalculationItem", "Expression": "RegEx.IsMatch(Expression,\"(?i)CALCULATE\\s*\\(\\s*[^,]+,\\s*(?i)FILTER\\s*\\(\\s*\\'*[A-Za-z0-9 _]+\\'*\\s*,\\s*\\[[^\\]]+\\]\")\r\nor\r\nRegEx.IsMatch(Expression,\"(?i)CALCULATETABLE\\s*\\([^,]*,\\s*(?i)FILTER\\s*\\(\\s*\\'*[A-Za-z0-9 _]+\\'*,\\s*\\[\")", "CompatibilityLevel": 1200}, {"ID": "INACTIVE_RELATIONSHIPS_THAT_ARE_NEVER_ACTIVATED", "Name": "[DAX Expressions] Inactive relationships that are never activated", "Category": "DAX Expressions", "Description": "Inactive relationships are activated using the USERELATIONSHIP function. If an inactive relationship is not referenced in any measure via this function, the relationship will not be used. It should be determined whether the relationship is not necessary or to activate the relationship via this method.\r\n\r\nReference: https://docs.microsoft.com/power-bi/guidance/relationships-active-inactive\r\nReference: https://dax.guide/userelationship/", "Severity": 2, "Scope": "Relationship", "Expression": "IsActive == false\r\nand not\r\n(\r\nModel.AllMeasures.Any(RegEx.IsMatch(Expression,\r\n\"(?i)USERELATIONSHIP\\s*\\(\\s*\\'*\" +\r\ncurrent.FromTable.Name + \"\\'*\\[\" + \r\ncurrent.FromColumn.Name + \"\\]\\s*,\\s*\\'*\" +\r\ncurrent.ToTable.Name + \"\\'*\\[\" +\r\ncurrent.ToColumn.Name + \"\\]\"))\r\nor\r\nModel.AllCalculationItems.Any(RegEx.IsMatch(Expression,\r\n\"(?i)USERELATIONSHIP\\s*\\(\\s*\\'*\" +\r\ncurrent.FromTable.Name + \"\\'*\\[\" + \r\ncurrent.FromColumn.Name + \"\\]\\s*,\\s*\\'*\" +\r\ncurrent.ToTable.Name + \"\\'*\\[\" +\r\ncurrent.ToColumn.Name + \"\\]\"))\r\n)", "CompatibilityLevel": 1200}, {"ID": "AVOID_USING_'1-(X/Y)'_SYNTAX", "Name": "[DAX Expressions] Avoid using '1-(x/y)' syntax", "Category": "DAX Expressions", "Description": "Instead of using the '1-(x/y)' or '1+(x/y)' syntax to achieve a percentage calculation, use the basic DAX functions (as shown below). Using the improved syntax will generally improve the performance. The '1+/-...' syntax always returns a value whereas the solution without the '1+/-...' does not (as the value may be 'blank'). Therefore the '1+/-...' syntax may return more rows/columns which may result in a slower query speed.\r\n\r\nLet's clarify with an example:\r\n\r\nAvoid this: 1 - SUM ( 'Sales'[CostAmount] ) / SUM( 'Sales'[SalesAmount] )\r\nBetter: DIVIDE ( SUM ( 'Sales'[SalesAmount] ) - SUM ( 'Sales'[CostAmount] ), SUM ( 'Sales'[SalesAmount] ) )\r\nBest: VAR x = SUM ( 'Sales'[SalesAmount] ) RETURN DIVIDE ( x - SUM ( 'Sales'[CostAmount] ), x )", "Severity": 2, "Scope": "Measure, CalculatedColumn, CalculationItem", "Expression": "RegEx.IsMatch(Expression,\"[0-9]+\\s*[-+]\\s*[\\(]*\\s*(?i)SUM\\s*\\(\\s*\\'*[A-Za-z0-9 _]+\\'*\\s*\\[[A-Za-z0-9 _]+\\]\\s*\\)\\s*\\/\")\r\nor\r\nRegEx.IsMatch(Expression,\"[0-9]+\\s*[-+]\\s*(?i)DIVIDE\\s*\\(\")", "CompatibilityLevel": 1200}, {"ID": "EVALUATEANDLOG_SHOULD_NOT_BE_USED_IN_PRODUCTION_MODELS", "Name": "[DAX Expressions] The EVALUATEANDLOG function should not be used in production models", "Category": "DAX Expressions", "Description": "The EVALUATEANDLOG function is meant to be used only in development/test environments and should not be used in production models.\r\n\r\nReference: https://pbidax.wordpress.com/2022/08/16/introduce-the-dax-evaluateandlog-function/", "Severity": 1, "Scope": "Measure", "Expression": "RegEx.IsMatch(Expression,\"(?i)EVALUATEANDLOG\\s*\\(\")", "CompatibilityLevel": 1200}, {"ID": "DATA_COLUMNS_MUST_HAVE_A_SOURCE_COLUMN", "Name": "[Error Prevention] Data columns must have a source column", "Category": "Error Prevention", "Description": "Data columns must have a source column. A data column without a source column will cause an error when processing the model.", "Severity": 3, "Scope": "DataColumn", "Expression": "string.IsNullOrWhitespace(SourceColumn)", "CompatibilityLevel": 1200}, {"ID": "EXPRESSION_RELIANT_OBJECTS_MUST_HAVE_AN_EXPRESSION", "Name": "[Error Prevention] Expression-reliant objects must have an expression", "Category": "Error Prevention", "Description": "Calculated columns, calculation items and measures must have an expression. Without an expression, these objects will not show any values.", "Severity": 3, "Scope": "Measure, CalculatedColumn, CalculationItem", "Expression": "string.IsNullOrWhiteSpace(Expression)", "CompatibilityLevel": 1200}, {"ID": "AVOID_STRUCTURED_DATA_SOURCES_WITH_PROVIDER_PARTITIONS", "Name": "[Error Prevention] Avoid structured data sources with provider partitions", "Category": "Error Prevention", "Description": "Power BI does not support provider (a.k.a. 'legacy') partitions which reference structured data sources. Partitions which reference structured data sources must use the M-language. Otherwise, 'provider' partitions must reference a 'provider' data source. This can be resolved by converting the structured data source into a provider data source (see 2nd reference link below).\r\n\r\nReference: https://docs.microsoft.com/power-bi/admin/service-premium-connect-tools#data-source-declaration\r\nReference: https://www.elegantbi.com/post/convertdatasources", "Severity": 2, "Scope": "Partition", "Expression": "SourceType == \"Query\"\r\nand\r\nDataSource.Type == \"Structured\"", "CompatibilityLevel": 1200}, {"ID": "AVOID_THE_USERELATIONSHIP_FUNCTION_AND_RLS_AGAINST_THE_SAME_TABLE", "Name": "[Error Prevention] Avoid the USERELATIONSHIP function and RLS against the same table", "Category": "Error Prevention", "Description": "The USERELATIONSHIP function may not be used against a table which also leverages row-level security (RLS). This will generate an error when using the particular measure in a visual. This rule will highlight the table which is used in a measure's USERELATIONSHIP function as well as RLS.\r\n\r\nReference: https://blog.crossjoin.co.uk/2013/05/10/userelationship-and-tabular-row-security/", "Severity": 3, "Scope": "Table, CalculatedTable", "Expression": "Model.AllMeasures.Any(RegEx.IsMatch(Expression,\"(?i)USERELATIONSHIP\\s*\\(\\s*.+?(?=])\\]\\s*,\\s*'*\" + current.Name + \"'*\\[\"))\r\nand\r\nRowLevelSecurity.Any(it <> null)", "CompatibilityLevel": 1200}, {"ID": "RELATIONSHIP_COLUMNS_SAME_DATA_TYPE", "Name": "[Error Prevention] Relationship columns should be of the same data type", "Category": "Error Prevention", "Description": "Columns used in a relationship should be of the same data type. Ideally, they will be of integer data type (see the related rule '[Formatting] Relationship columns should be of integer data type'). Having columns within a relationship which are of different data types may lead to various issues.", "Severity": 2, "Scope": "Relationship", "Expression": "FromColumn.DataType != ToColumn.DataType", "CompatibilityLevel": 1200}, {"ID": "AVOID_INVALID_NAME_CHARACTERS", "Name": "[Error Prevention] Avoid invalid characters in names", "Category": "Error Prevention", "Description": "This rule identifies if a name for a given object in your model (i.e. table/column/measure) which contains an invalid character. Invalid characters will cause an error when deploying the model (and failure to deploy). This rule has a fix expression which converts the invalid character into a space, resolving the issue.", "Severity": 3, "Scope": "Table, Measure, Hierarchy, Level, Perspective, Partition, DataColumn, CalculatedColumn, CalculatedTable, CalculatedTableColumn, KPI, ModelRole, CalculationGroup, CalculationItem", "Expression": "Name.ToCharArray().Any(char.IsControl(it) and !char.IsWhiteSpace(it))", "FixExpression": "Name = string.Concat( it.Name.ToCharArray().Select( c => (char.<PERSON>ontrol(c) && !char.IsWhiteSpace(c)) ? ' ': c ))", "CompatibilityLevel": 1200}, {"ID": "AVOID_INVALID_DESCRIPTION_CHARACTERS", "Name": "[Error Prevention] Avoid invalid characters in descriptions", "Category": "Error Prevention", "Description": "This rule identifies if a description for a given object in your model (i.e. table/column/measure) which contains an invalid character. Invalid characters will cause an error when deploying the model (and failure to deploy). This rule has a fix expression which converts the invalid character into a space, resolving the issue.", "Severity": 3, "Scope": "Table, Measure, Hierarchy, Level, Perspective, Partition, DataColumn, CalculatedColumn, CalculatedTable, CalculatedTableColumn, KPI, ModelRole, CalculationGroup, CalculationItem", "Expression": "Description.ToCharArray().Any(char.IsControl(it) and !char.IsWhiteSpace(it))", "FixExpression": "Description = string.Concat( it.Description.ToCharArray().Select( c => (char.Is<PERSON>ontrol(c) && !char.IsWhiteSpace(c)) ? ' ': c ))", "CompatibilityLevel": 1200}, {"ID": "SET_ISAVAILABLEINMDX_TO_TRUE_ON_NECESSARY_COLUMNS", "Name": "[Error Prevention] Set IsAvailableInMdx to true on necessary columns", "Category": "Error Prevention", "Description": "In order to avoid errors, ensure that attribute hierarchies are enabled if a column is used for sorting another column, used in a hierarchy, used in variations, or is sorted by another column.", "Severity": 3, "Scope": "DataColumn, CalculatedColumn, CalculatedTableColumn", "Expression": "IsAvailableInMDX = false\r\n\r\nand\r\n(\r\nUsedInSortBy.Any()\r\nor\r\nUsedInHierarchies.Any()\r\nor\r\nUsedInVariations.Any()\r\nor\r\nSortByColumn != null\r\n)", "FixExpression": "IsAvailableInMDX = true", "CompatibilityLevel": 1200}, {"ID": "UNNECESSARY_COLUMNS", "Name": "[Maintenance] Remove unnecessary columns", "Category": "Maintenance", "Description": "Hidden columns that are not referenced by any DAX expressions, relationships, hierarchy levels or Sort By-properties should be removed.", "Severity": 3, "Scope": "DataColumn, CalculatedColumn, CalculatedTableColumn", "Expression": "(IsHidden or Table.IsHidden)\n\n\r\nand ReferencedBy.Count = 0\r\n\n\nand (not UsedInRelationships.Any())\n\n\r\nand (not UsedInSortBy.Any())\n\n\r\nand (not UsedInHierarchies.Any())\n\n\r\nand (not Table.RowLevelSecurity.Any(\nit <> null and it.IndexOf(\"[\" + current.Name + \"]\", \"OrdinalIgnoreCase\") >= 0\n))\n\n and (not Model.Roles.Any(RowLevelSecurity.Any(\nit <> null and \n(\nit.IndexOf(current.Table.Name + \"[\" + current.Name + \"]\", \"OrdinalIgnoreCase\") >= 0 or\n it.IndexOf(\"'\" + current.Table.Name + \"'[\" + current.Name + \"]\", \"OrdinalIgnoreCase\") >= 0\n )\n)))\r\nand not (\r\nObjectLevelSecurity.Any(it.ToString() == \"None\"))\r\nand not (\r\nTable.ObjectLevelSecurity.Any(it.ToString() == \"None\"))", "FixExpression": "Delete()", "CompatibilityLevel": 1200}, {"ID": "UNNECESSARY_MEASURES", "Name": "[Maintenance] Remove unnecessary measures", "Category": "Maintenance", "Description": "Hidden measures that are not referenced by any DAX expressions should be removed for maintainability", "Severity": 2, "Scope": "Measure", "Expression": "(Table.IsHidden or IsHidden) \r\nand ReferencedBy.Count = 0", "FixExpression": "Delete()", "CompatibilityLevel": 1200}, {"ID": "FIX_REFERENTIAL_INTEGRITY_VIOLATIONS", "Name": "[Maintenance] Fix referential integrity violations", "Category": "Maintenance", "Description": "This rule highlights relationships which have referential integrity violations. This indicates that there are values in the table on the 'from' side of the relationship which do not exist in the table on the 'to' side of the relationship. Referential integrity violations will also produce the 'blank' member value in slicers. It is recommended to fix these issues by ensuring that the 'to' table's primary key column has all the values in the 'from' table's foreign key column.\r\n\r\nReference: https://blog.enterprisedna.co/vertipaq-analyzer-tutorial-relationships-referential-integrity/", "Severity": 2, "Scope": "Relationship", "Expression": "Convert.ToInt64(GetAnnotation(\"Vertipaq_RIViolationInvalidRows\")) > 0", "CompatibilityLevel": 1200}, {"ID": "REMOVE_DATA_SOURCES_NOT_REFERENCED_BY_ANY_PARTITIONS", "Name": "[Maintenance] Remove data sources not referenced by any partitions", "Category": "Maintenance", "Description": "Data sources which are not referenced by any partitions may be removed.", "Severity": 1, "Scope": "ProviderDataSource, StructuredDataSource", "Expression": "UsedByPartitions.Count() == 0\r\nand not Model.Tables.Any(SourceExpression.Contains(OuterIt.Name))\r\nand not Model.AllPartitions.Any(Query.Contains(OuterIt.Name))", "FixExpression": "Delete()", "CompatibilityLevel": 1200}, {"ID": "ENSURE_TABLES_HAVE_RELATIONSHIPS", "Name": "[Maintenance] Ensure tables have relationships", "Category": "Maintenance", "Description": "This rule highlights tables which are not connected to any other table in the model with a relationship.", "Severity": 1, "Scope": "Table, CalculatedTable", "Expression": "UsedInRelationships.Count() == 0", "CompatibilityLevel": 1200}, {"ID": "PERSPECTIVES_WITH_NO_OBJECTS", "Name": "[Maintenance] Perspectives with no objects", "Category": "Maintenance", "Description": "Perspectives that contain no objects (tables) are most likely not necessary. In this rule, it is only necessary to check tables as adding a column/measure/hierarchy to a perspective also adds the table to the perspective. Additionally, tables in general covers calculated tables and calculation groups as well.", "Severity": 1, "Scope": "Perspective", "Expression": "Model.Tables.Any(InPerspective[current.Name]) == false", "FixExpression": "Delete()", "CompatibilityLevel": 1200}, {"ID": "CALCULATION_GROUPS_WITH_NO_CALCULATION_ITEMS", "Name": "[Maintenance] Calculation groups with no calculation items", "Category": "Maintenance", "Description": "Calculation groups have no function unless they have calculation items.", "Severity": 2, "Scope": "CalculationGroup", "Expression": "CalculationItems.Count == 0", "CompatibilityLevel": 1200}, {"ID": "PARTITION_NAME_SHOULD_MATCH_TABLE_NAME_FOR_SINGLE_PARTITION_TABLES", "Name": "[Naming Conventions] Partition name should match table name for single partition tables", "Category": "Naming Conventions", "Description": "Tables with just one partition should match their table and partition names.Tables with more than one partition should have each partition name starting with the table name.", "Severity": 1, "Scope": "Table", "Expression": "(Partitions.Count = 1 and Partitions[0].Name <> Name)", "FixExpression": "Partitions[0].Name = it.Name", "CompatibilityLevel": 1200}, {"ID": "SPECIAL_CHARS_IN_OBJECT_NAMES", "Name": "[Naming Conventions] Object names must not contain special characters", "Category": "Naming Conventions", "Description": "Tabs, line breaks, etc.", "Severity": 2, "Scope": "Model, Table, Measure, Hierarchy, Perspective, Partition, DataColumn, CalculatedColumn, CalculatedTable, CalculatedTableColumn, CalculationGroup, CalculationItem", "Expression": "Name.IndexOf(char(9)) > -1\r\nor\r\n\nName.IndexOf(char(10)) > -1 \r\nor\r\n\nName.IndexOf(char(13)) > -1", "CompatibilityLevel": 1200}, {"ID": "TRIM_OBJECT_NAMES", "Name": "[Naming Conventions] Trim object names", "Category": "Naming Conventions", "Description": "Unintentionally leaving a trailing space in an object name is a common occurrence when copying/duplicating objects in Tabular Editor.", "Severity": 1, "Scope": "Model, Table, Measure, Hierarchy, Level, Perspective, Partition, ProviderDataSource, DataColumn, CalculatedColumn, CalculatedTable, CalculatedTableColumn, StructuredDataSource, NamedExpression, ModelRole, CalculationGroup, CalculationItem", "Expression": "Name.StartsWith(\" \") or Name.EndsWith(\" \")", "CompatibilityLevel": 1200}, {"ID": "FORMAT_FLAG_COLUMNS_AS_YES/NO_VALUE_STRINGS", "Name": "[Formatting] Format flag columns as Yes/No value strings", "Category": "Formatting", "Description": "Flags must be properly formatted as Yes/No as this is easier to read than using 0/1 integer values.", "Severity": 1, "Scope": "DataColumn, CalculatedColumn, CalculatedTableColumn", "Expression": "(\nName.StartsWith(\"Is\") and \nDataType = \"Int64\" and \nnot (IsHidden or Table.IsHidden)\n) \r\nor\r\n\n(\nName.EndsWith(\" Flag\") and \nDataType <> \"String\" and \nnot (IsHidden or Table.IsHidden)\n)", "CompatibilityLevel": 1200}, {"ID": "OBJECTS_SHOULD_NOT_START_OR_END_WITH_A_SPACE", "Name": "[Formatting] Objects should not start or end with a space", "Category": "Formatting", "Description": "Objects should not start or end with a space", "Severity": 3, "Scope": "Model, Table, Measure, Hierarchy, Perspective, Partition, DataColumn, CalculatedColumn", "Expression": "Name.StartsWith(\" \") or Name.EndsWith(\" \")", "CompatibilityLevel": 1200}, {"ID": "PROVIDE_FORMAT_STRING_FOR_MEASURES", "Name": "[Formatting] Provide format string for measures", "Category": "Formatting", "Description": "Visible measures should have their format string property assigned", "Severity": 3, "Scope": "Measure", "Expression": "not IsHidden \r\nand not Table.IsHidden \r\nand string.IsNullOrWhitespace(FormatString)", "CompatibilityLevel": 1200}, {"ID": "ADD_DATA_CATEGORY_FOR_COLUMNS", "Name": "[Formatting] Add data category for columns", "Category": "Formatting", "Description": "Add Data Category property for appropriate columns.\r\n\r\nReference: https://docs.microsoft.com/power-bi/transform-model/desktop-data-categorization", "Severity": 1, "Scope": "DataColumn, CalculatedColumn, CalculatedTableColumn", "Expression": "string.IsNullOrWhitespace(DataCategory)\r\nand\r\n(\r\n(\r\nName.ToLower().Contains(\"country\")\r\nor \r\n\nName.ToLower().Contains(\"continent\"\n)\r\nor\r\nName.ToLower().Contains(\"city\")\r\n)\r\nand DataType == \"String\"\r\n)\r\nor \r\n(\r\n(\nName.ToLower() == \"latitude\" \n or \nName.ToLower() == \"longitude\")\r\nand (DataType == DataType.Decimal or DataType == DataType.Double)\r\n)", "CompatibilityLevel": 1200}, {"ID": "HIDE_FOREIGN_KEYS", "Name": "[Formatting] Hide foreign keys", "Category": "Formatting", "Description": "Foreign keys should always be hidden.", "Severity": 2, "Scope": "DataColumn, CalculatedColumn, CalculatedTableColumn", "Expression": "UsedInRelationships.Any(FromColumn.Name == current.Name and FromCardinality == \"Many\")\n\r\nand\r\n\nIsHidden == false", "FixExpression": "IsHidden = true", "CompatibilityLevel": 1200}, {"ID": "MARK_PRIMARY_KEYS", "Name": "[Formatting] Mark primary keys", "Category": "Formatting", "Description": "Set the 'Key' property to 'True' for primary key columns within the column properties.", "Severity": 1, "Scope": "DataColumn, CalculatedColumn, CalculatedTableColumn", "Expression": "UsedInRelationships.Any(ToTable.Name == current.Table.Name and ToColumn.Name == current.Name and ToCardinality == \"One\")\r\n\nand\r\n\nIsKey == false\r\nand\r\ncurrent.Table.DataCategory != \"Time\"", "FixExpression": "IsKey = true", "CompatibilityLevel": 1200}, {"ID": "HIDE_FACT_TABLE_COLUMNS", "Name": "[Formatting] Hide fact table columns", "Category": "Formatting", "Description": "It is a best practice to hide fact table columns that are used for aggregation in measures.", "Severity": 2, "Scope": "DataColumn, CalculatedColumn, CalculatedTableColumn", "Expression": "(\r\nReferencedBy.AllMeasures.Any(RegEx.IsMatch(Expression,\"(?i)COUNT\\s*\\(\\s*\\'*\" + outerit.Table.Name + \"\\'*\\[\" + outerit.Name + \"\\]\\s*\\)\"))\r\n\nor\r\nReferencedBy.AllMeasures.Any(RegEx.IsMatch(Expression,\"(?i)COUNTBLANK\\s*\\(\\s*\\'*\" + outerit.Table.Name + \"\\'*\\[\" + outerit.Name + \"\\]\\s*\\)\"))\r\n\nor\r\nReferencedBy.AllMeasures.Any(RegEx.IsMatch(Expression,\"(?i)SUM\\s*\\(\\s*\\'*\" + outerit.Table.Name + \"\\'*\\[\" + outerit.Name + \"\\]\\s*\\)\"))\r\nor\r\nReferencedBy.AllMeasures.Any(RegEx.IsMatch(Expression,\"(?i)AVERAGE\\s*\\(\\s*\\'*\" + outerit.Table.Name + \"\\'*\\[\" + outerit.Name + \"\\]\\s*\\)\"))\r\n\nor\r\nReferencedBy.AllMeasures.Any(RegEx.IsMatch(Expression,\"(?i)VALUES\\s*\\(\\s*\\'*\" + outerit.Table.Name + \"\\'*\\[\" + outerit.Name + \"\\]\\s*\\)\"))\r\n\nor\r\nReferencedBy.AllMeasures.Any(RegEx.IsMatch(Expression,\"(?i)DISTINCT\\s*\\(\\s*\\'*\" + outerit.Table.Name + \"\\'*\\[\" + outerit.Name + \"\\]\\s*\\)\"))\r\nor\r\nReferencedBy.AllMeasures.Any(RegEx.IsMatch(Expression,\"(?i)DISTINCTCOUNT\\s*\\(\\s*\\'*\" + outerit.Table.Name + \"\\'*\\[\" + outerit.Name + \"\\]\\s*\\)\"))\r\n\nor\n\r\nReferencedBy.AllMeasures.Any(RegEx.IsMatch(Expression,\"(?i)MIN\\s*\\(\\s*\\'*\" + outerit.Table.Name + \"\\'*\\[\" + outerit.Name + \"\\]\\s*\\)\"))\r\n\nor\n\r\nReferencedBy.AllMeasures.Any(RegEx.IsMatch(Expression,\"(?i)MAX\\s*\\(\\s*\\'*\" + outerit.Table.Name + \"\\'*\\[\" + outerit.Name + \"\\]\\s*\\)\"))\r\n\nor\r\nReferencedBy.AllMeasures.Any(RegEx.IsMatch(Expression,\"(?i)COUNTA\\s*\\(\\s*\\'*\" + outerit.Table.Name + \"\\'*\\[\" + outerit.Name + \"\\]\\s*\\)\"))\n\r\n\nor\r\nReferencedBy.AllMeasures.Any(RegEx.IsMatch(Expression,\"(?i)AVERAGEA\\s*\\(\\s*\\'*\" + outerit.Table.Name + \"\\'*\\[\" + outerit.Name + \"\\]\\s*\\)\"))\r\n\nor\r\nReferencedBy.AllMeasures.Any(RegEx.IsMatch(Expression,\"(?i)MAXA\\s*\\(\\s*\\'*\" + outerit.Table.Name + \"\\'*\\[\" + outerit.Name + \"\\]\\s*\\)\"))\r\n\nor\r\nReferencedBy.AllMeasures.Any(RegEx.IsMatch(Expression,\"(?i)MINA\\s*\\(\\s*\\'*\" + outerit.Table.Name + \"\\'*\\[\" + outerit.Name + \"\\]\\s*\\)\"))\r\n)\r\n\nand IsHidden == false\r\n\nand (DataType == \"Int64\" || DataType == \"Decimal\" || DataType == \"Double\")", "FixExpression": "IsHidden = true", "CompatibilityLevel": 1200}, {"ID": "MONTH_(AS_A_STRING)_MUST_BE_SORTED", "Name": "[Formatting] Month (as a string) must be sorted", "Category": "Formatting", "Description": "This rule highlights month columns which are strings and are not sorted. If left unsorted, they will sort alphabetically (i.e. April, August...). Make sure to sort such columns so that they sort properly (January, February, March...).", "Severity": 2, "Scope": "DataColumn, CalculatedColumn, CalculatedTableColumn", "Expression": "Name.ToUpper().Contains(\"MONTH\")\r\nand\r\n! Name.ToUpper().Contains(\"MONTHS\") \r\nand \r\n\n\nDataType == DataType.String \r\nand \r\nSortByColumn == null", "CompatibilityLevel": 1200}]