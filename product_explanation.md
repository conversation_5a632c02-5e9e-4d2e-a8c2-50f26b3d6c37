# Product Explanation: Power BI Semantic Model and Report

This document explains the step-by-step process of creating a Power BI semantic model and an empty report, based on the provided requirements and data source.

## 1. Project Setup

Initially, the project repository was cloned. Due to an incorrect cloning command, a nested directory structure was created. This was corrected by moving the contents of the nested directory to the parent directory and removing the empty nested directory.

## 2. Requirements Analysis

To understand the project requirements and the available data, the following files were analyzed:

*   `README.md`: Provided an overview of the project and the expected AI agent workflow.
*   `requirements-01.md`: Detailed the business requirements for the semantic model, including specific measures and data source information.
*   `AzureSQLSchema.csv`: Contained the schema of the SQL Server database, which was crucial for identifying relevant tables and columns.

## 3. Development Specification

Based on the analysis, a `development-spec.md` file was created. This document outlined the plan for building the semantic model, including the data model (fact and dimension tables), measures, and the expected TMDL and PBIP file structure.

## 4. Semantic Model Creation (TMDL Files)

The core of the semantic model was built using TMDL (Tabular Model Definition Language) files, which define the tables, columns, measures, and relationships. The following TMDL files were created in the `src/Sales.SemanticModel/definition` directory:

*   `Calendar.tmdl`: Reused from an existing template, defining the Calendar dimension table.
*   `Product.tmdl`: Created by combining `DimProduct`, `DimProductSubcategory`, and `DimProductCategory` from the data source into a single Product dimension.
*   `Sales.tmdl`: Created by combining `FactInternetSales` and `FactResellerSales` into a single Sales fact table. This file also includes the DAX measures for Total Sales Amount, Year-over-Year Growth, Month-over-Month Growth, and Product Sales Rank.
*   `relationships.tmdl`: Defined the relationships between the Sales, Product, and Calendar tables.
*   `model.tmdl`: The main model definition file, including culture and Power BI data source version.
*   `expressions.tmdl`: Defined semantic model parameters for the server and database names.

## 5. Power BI Project Files (PBIP Files)

To ensure the project could be opened and managed by Power BI Desktop, the following PBIP files were created:

*   `definition.pbism`: Located in `src/Sales.SemanticModel`, this file defines the semantic model and its format (TMDL).
*   `definition.pbir`: Located in `src/Sales.Report`, this file defines an empty Power BI report and links it to the semantic model.
*   `Sales.pbip`: The main project file in the `src` directory, which serves as a shortcut to open the report and its associated semantic model in Power BI Desktop.

## 6. Troubleshooting and Validation

Throughout the development process, the Best Practice Analyzer (BPA) script (`.bpa/bpa.ps1`) was used to validate the semantic model. Several issues were identified and resolved:

*   **TMDL Format Errors (Indentation, Keywords):** Initial attempts to validate the TMDL files revealed strict indentation requirements and unsupported keywords (`changedProperty`). These were corrected by carefully adjusting the file content and removing invalid properties.
*   **Relationship Data Type Mismatch:** The relationship between the Sales and Calendar tables initially had a data type mismatch (`int64` vs. `dateTime`). This was resolved by ensuring both columns used for the relationship were of a compatible `dateTime` type and updating the M expressions accordingly.
*   **Missing Format Strings for Measures:** The BPA flagged measures lacking explicit format strings. These were added to ensure proper display in Power BI.
*   **`isAvailableInMdx` Property:** Warnings related to `isAvailableInMdx` for non-attribute columns were addressed by setting this property to `false` for relevant columns (e.g., foreign keys and fact table columns not used as measures).
*   **PBIP File Schema Errors (`byName`, `datasetReference`, `format`):** Errors encountered when opening the `.pbip` file in Power BI Desktop indicated schema validation issues in `definition.pbir` and `definition.pbism`. The `byName` property was removed from `datasetReference` in `definition.pbir`. For `definition.pbism`, after several iterations, it was determined that the simplest form `{"version": "4.0"}` was the correct and compatible structure for the Power BI Desktop version being used.

## 7. How to Use

To open and explore the created Power BI semantic model and report, simply open the `src/Sales.pbip` file using Power BI Desktop. This will load the report and its associated semantic model, allowing you to visualize and analyze the data.