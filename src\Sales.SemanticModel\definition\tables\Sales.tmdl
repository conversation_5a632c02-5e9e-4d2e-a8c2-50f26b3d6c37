table Sales

	column OrderDate<PERSON>ey
		dataType: int64
		sourceColumn: OrderDateKey

	column ProductKey
		dataType: int64
		sourceColumn: ProductKey
		isHidden
		isAvailableInMdx: false

	column SalesAmount
		dataType: decimal
		sourceColumn: SalesAmount
		isHidden
		isAvailableInMdx: false

	column FullDateAlternateKey
		dataType: dateTime
		sourceColumn: FullDateAlternateKey
		isHidden
		isAvailableInMdx: false

	/// [Total Sales Amount] Sum of SalesAmount from the combined Sales table.
	measure 'Total Sales Amount' = SUM(Sales[SalesAmount])
		formatString: #,##0

	/// [Sales YoY Growth %] Year-over-year sales growth.
	measure 'Sales YoY Growth %' = 
		```
		DIVIDE(
			[Total Sales Amount] - CALCULATE([Total Sales Amount], SAMEPERIODLASTYEAR('Calendar'[Date])),
			CALCULATE([Total Sales Amount], SAMEPERIODLASTYEAR('Calendar'[Date]))
		)
		```
		formatString: "0.00%"

	/// [Sales MoM Growth %] Month-over-month sales growth.
	measure 'Sales MoM Growth %' = 
		```
		DIVIDE(
			[Total Sales Amount] - CALCULATE([Total Sales Amount], PREVIOUSMONTH('Calendar'[Date])),
			CALCULATE([Total Sales Amount], PREVIOUSMONTH('Calendar'[Date]))
		)
		```
		formatString: "0.00%"

	/// [Product Sales Rank] Rank of products by sales amount.
	measure 'Product Sales Rank' = RANKX(ALLSELECTED(Product), [Total Sales Amount],, DESC)
		formatString: #,##0

	partition Sales = m
		mode: import
		source = ```
			let
			    Source = Sql.Database(#"Parameter_Server", #"Parameter_Database"),
			    FactInternetSales = Source{[Schema="dbo",Item="FactInternetSales"]}[Data],
			    FactResellerSales = Source{[Schema="dbo",Item="FactResellerSales"]}[Data],
			    CombinedSales = Table.Combine({FactInternetSales, FactResellerSales}),
			    ChangedType = Table.TransformColumnTypes(CombinedSales,{{"OrderDate", type date}}),
			    RenamedColumns = Table.RenameColumns(ChangedType,{{"OrderDate", "FullDateAlternateKey"}}),
			    SelectedColumns = Table.SelectColumns(RenamedColumns,{"OrderDateKey", "ProductKey", "SalesAmount", "FullDateAlternateKey"})
			in
			    SelectedColumns
			```