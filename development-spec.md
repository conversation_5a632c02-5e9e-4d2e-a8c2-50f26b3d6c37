
# Development Specification

This document outlines the plan for creating the 'Sales' semantic model based on the provided requirements.

## 1. Data Model

The data model will be a star schema with one fact table and two dimension tables.

### Fact Table: Sales

A new `Sales` table will be created by combining `FactInternetSales` and `FactResellerSales`. It will contain the following columns:

*   `OrderDateKey`
*   `ProductKey`
*   `SalesAmount`

### Dimension Table: Product

A new `Product` table will be created by joining `DimProduct`, `DimProductSubcategory`, and `DimProductCategory`. It will contain the following columns:

*   `ProductKey`
*   `EnglishProductName`
*   `EnglishProductCategoryName`

### Dimension Table: Calendar

The existing `Calendar.tmdl` will be used for time-related calculations.

## 2. Measures

The following measures will be created in the `Sales.tmdl` file:

*   **[Total Sales Amount]**: `SUM(Sales[SalesAmount])`
*   **[Sales YoY Growth %]**: `DIVIDE([Total Sales Amount] - [Total Sales Amount LY], [Total Sales Amount LY])`
*   **[Sales MoM Growth %]**: `DIVIDE([Total Sales Amount] - [Total Sales Amount PM], [Total Sales Amount PM])`
*   **[Product Sales Rank]**: `RANKX(ALLSELECTED(Product), [Total Sales Amount],, DESC)`

## 3. TMDL and PBIP File Structure

The following file structure will be created in the `/src` directory:

```
/src
    /Sales.SemanticModel
        /definition
            /tables
                Sales.tmdl
                Product.tmdl
                Calendar.tmdl
            relationships.tmdl
            model.tmdl
            expressions.tmdl
        definition.pbism
    /Sales.Report
        definition.pbir
    Sales.pbip
```

## 4. Data Source Parameters

The following parameters will be created in `expressions.tmdl`:

*   `Parameter_Server`: "dummyserver.database.windows.net"
*   `Parameter_Database`: "AdventureWorksDW"
