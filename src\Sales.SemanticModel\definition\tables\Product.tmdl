table Product

	column ProductKey
		dataType: int64
		isKey
		sourceColumn: ProductKey
		isHidden
		isAvailableInMdx: false

	column ProductName
		dataType: string
		sourceColumn: EnglishProductName

	column ProductCategory
		dataType: string
		sourceColumn: EnglishProductCategoryName

	partition Product = m
		mode: import
		source = ```
			let
			    Source = Sql.Database(#"Parameter_Server", #"Parameter_Database"),
			    DimProduct = Source{[Schema="dbo",Item="DimProduct"]}[Data],
			    DimProductSubcategory = Source{[Schema="dbo",Item="DimProductSubcategory"]}[Data],
			    DimProductCategory = Source{[Schema="dbo",Item="DimProductCategory"]}[Data],
			    ProductSubcategory = Table.NestedJoin(DimProduct, {"ProductSubcategoryKey"}, DimProductSubcategory, {"ProductSubcategoryKey"}, "DimProductSubcategory", JoinKind.LeftOuter),
			    ProductCategory = Table.NestedJoin(ProductSubcategory, {"ProductCategoryKey"}, DimProductCategory, {"ProductCategoryKey"}, "DimProductCategory", JoinKind.LeftOuter),
			    ExpandedProductSubcategory = Table.ExpandTableColumn(ProductCategory, "DimProductSubcategory", {"EnglishProductSubcategoryName"}),
			    ExpandedProductCategory = Table.ExpandTableColumn(ExpandedProductSubcategory, "DimProductCategory", {"EnglishProductCategoryName"}),
			    SelectedColumns = Table.SelectColumns(ExpandedProductCategory,{"ProductKey", "EnglishProductName", "EnglishProductCategoryName"})
			in
			    SelectedColumns
			```